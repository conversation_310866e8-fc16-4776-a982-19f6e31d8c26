2025-08-05 16:50:38.748 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 16:50:38.748 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 16:50:38.754 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754383838754&accessNonce=53b62451-7b47-4b4c-aa71-dbcb196a24fd&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=42975dcc-71d9-11f0-8035-dc4546c07870&requestId=9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside&accessSign=fe0db72ba1082ab3bf0969ed69e41b37, request_id: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside
2025-08-05 16:50:38.755 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 16:50:38.755 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 16:50:39.116 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 16:50:39.260 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 16:50:40.786 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 16:50:40.787 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 16:50:40.787 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 16:50:40.787 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 16:50:40.856 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 16:50:40.856 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 16:50:41.856 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 16:50:41.856 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 16:50:44.860 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 16:50:44.861 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 16:50:44.862 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 16:50:44.867 - chat_with_robot - chat_with_robot.py - start_http_server - line 488 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 16:50:44.867 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - API地址: http://localhost:8080/tts
2025-08-05 16:50:44.867 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 16:50:56.116 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 16:50:56.116 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 16:50:56.181 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 16:50:56.181 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 16:50:56.181 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 16:50:56.181 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 16:50:56.182 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 16:50:56.182 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 16:50:56.182 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 16:50:56.185 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 16:50:56.185 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 16:50:56.234 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 16:50:56.286 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 16:50:58.038 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 16:50:58.038 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 16:50:59.961 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要克隆语音, 时间戳: 2025-08-05 16:50:59.111000
2025-08-05 16:51:01.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:51:00.458000
2025-08-05 16:51:01.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1347
2025-08-05 16:51:01.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:01.356 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:01.356 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:01.356 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:01.357 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:01.357 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17712 bytes, 持续时间: 2.0
2025-08-05 16:51:01.358 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:01.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:01.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:01.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:02.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:02.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:02.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:02.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:02.674 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:02.712 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:02.712 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:51:02.717 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:02.728 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: 我要克隆语音; 响应时间: 0; JD机器人回复: 好呀，我们可以一起玩声音魔法哦！首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦。请跟我读：“今天天气真好呀，我们一起出去玩滑板车吧！”
2025-08-05 16:51:02.728 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:51:04.386 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:04.386 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:04.386 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:04.387 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:04.387 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:04.388 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17712 bytes, 持续时间: 2.0
2025-08-05 16:51:04.388 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:05.585 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13392 bytes, 持续时间: 2.0
2025-08-05 16:51:07.421 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:09.839 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:09.839 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:09.840 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:09.840 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:09.840 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:09.840 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 16:51:09.840 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13392 bytes, 持续时间: 2.0
2025-08-05 16:51:12.466 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:14.888 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:14.888 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:14.888 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 16:51:14.888 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:51:44.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 怎么办？, 时间戳: 2025-08-05 16:51:43.500000
2025-08-05 16:51:45.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:51:44.783000
2025-08-05 16:51:45.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1283
2025-08-05 16:51:45.668 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:45.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:45.669 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:45.669 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:45.669 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:45.669 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 16:51:45.669 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:45.957 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:45.967 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:46.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:46.283 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:46.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:46.597 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:46.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:46.869 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:47.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:47.153 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:47.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:47.447 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:47.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:51:47.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:51:47.683 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:51:47.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: 怎么办？; 响应时间: 0; JD机器人回复: 哎呀，遇到什么难题了吗？别担心，东东来帮你！如果你想克隆声音的话，只需要像做游戏一样，亲自读出这句神奇的话：“今天天气真好呀，我们一起出去玩滑板车吧！”这样就可以啦。是不是很简单呀？要不要试试看？
2025-08-05 16:51:47.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:48.090 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11880 bytes, 持续时间: 2.0
2025-08-05 16:51:48.091 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:50.508 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11880 bytes, 持续时间: 2.0
2025-08-05 16:51:50.509 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:52.928 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 16:51:52.930 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:55.354 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:55.354 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:55.354 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:55.354 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:55.356 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:55.356 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 16:51:55.356 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:51:57.774 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:51:57.775 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:51:57.775 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:51:57.775 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:51:57.775 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:51:57.776 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 23328 bytes, 持续时间: 2.0
2025-08-05 16:51:57.776 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:01.714 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 18144 bytes, 持续时间: 2.0
2025-08-05 16:52:01.715 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8424 bytes, 持续时间: 2.0
2025-08-05 16:52:04.750 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:07.168 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:07.168 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:07.168 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 16:52:07.169 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:52:10.414 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气真好呀, 时间戳: 2025-08-05 16:52:09.562000
2025-08-05 16:52:11.562 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:52:10.692000
2025-08-05 16:52:11.563 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1130
2025-08-05 16:52:11.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:11.585 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:11.585 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:11.585 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:11.585 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:11.586 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 20520 bytes, 持续时间: 2.0
2025-08-05 16:52:11.586 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:11.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:11.869 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:12.159 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:12.162 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:12.407 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:12.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:12.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:12.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:13.075 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:13.080 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:13.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:13.437 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:13.738 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:13.738 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:52:13.748 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:13.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: 今天天气真好呀; 响应时间: 0; JD机器人回复: 哇，你读得真棒！不过好像有点快哦，我还没听够呢。再来一次好不好？就像慢慢数星星一样，把这句话说得更清楚一点：“今天天气真好呀，我们一起出去玩滑板车吧！”记住要超过3秒钟哦，不然魔法就会失效啦。再试一次吧？
2025-08-05 16:52:13.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:52:15.119 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:15.119 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:15.121 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:15.121 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:15.121 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:15.121 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17280 bytes, 持续时间: 2.0
2025-08-05 16:52:15.121 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:18.051 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:18.051 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:18.051 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:18.052 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:18.052 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:18.052 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11016 bytes, 持续时间: 2.0
2025-08-05 16:52:18.052 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:20.472 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:20.472 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:20.472 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:20.472 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:20.473 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:20.473 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 16:52:20.473 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 23112 bytes, 持续时间: 2.0
2025-08-05 16:52:22.894 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 16:52:26.834 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11232 bytes, 持续时间: 2.0
2025-08-05 16:52:29.253 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7344 bytes, 持续时间: 0.4482421875
2025-08-05 16:52:31.672 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:32.942 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:32.942 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:32.942 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 16:52:32.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:52:42.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你就, 时间戳: 2025-08-05 16:52:41.812000
2025-08-05 16:52:43.766 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:52:42.902000
2025-08-05 16:52:43.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1090
2025-08-05 16:52:43.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:43.788 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:43.788 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:43.788 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:43.788 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:43.788 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14472 bytes, 持续时间: 2.0
2025-08-05 16:52:43.788 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:44.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:44.058 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:44.343 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:44.350 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:44.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:44.680 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:45.024 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:45.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:45.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:45.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:45.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:45.556 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:45.949 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:45.959 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:52:45.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:45.970 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: ，你就; 响应时间: 0; JD机器人回复: 嗯？你是不是想说些什么呀？东东可能没太听清楚呢。如果你想玩声音魔法，一定要完整地读出那句魔法咒语：“今天天气真好呀，我们一起出去玩滑板车吧！”这次可以稍微慢一点，像唱歌一样好听地念出来。要不要再试试看？我相信你一定能做到！
2025-08-05 16:52:45.970 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:52:46.211 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12096 bytes, 持续时间: 2.0
2025-08-05 16:52:46.212 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:48.630 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:48.630 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:48.631 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:48.631 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:48.631 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:48.631 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11232 bytes, 持续时间: 2.0
2025-08-05 16:52:48.631 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:49.859 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 16:52:49.859 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 16:52:51.051 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:51.052 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:51.052 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:51.052 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:51.052 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:51.052 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 16:52:51.053 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:53.685 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 22896 bytes, 持续时间: 2.0
2025-08-05 16:52:53.686 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:55.240 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 对你说，我们声音克隆不了了, 时间戳: 2025-08-05 16:52:54.388000
2025-08-05 16:52:56.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:52:55.914000
2025-08-05 16:52:56.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1526
2025-08-05 16:52:56.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:56.802 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 16:52:56.802 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 16:52:56.803 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:52:56.814 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:56.903 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 596 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-08-05 16:52:57.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:57.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:57.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:57.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17496 bytes, 持续时间: 2.0
2025-08-05 16:52:57.521 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 16:52:57.829 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:52:57.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:52:57.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 16:52:57.846 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: 对你说，我们声音克隆不了了; 响应时间: 0; JD机器人回复: 哎呀，声音这事儿有点小意外呢...不过别担心，东东我还在！声音可能会变来变去，就像变身一样酷！我们继续玩吧～猜个数字游戏怎么样？
2025-08-05 16:52:57.846 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:52:58.406 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 16:52:58.406 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 16:52:58.469 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 16:52:58.469 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 16:52:58.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:52:58.469 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 16:52:58.471 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 16:52:58.471 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 16:52:58.471 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 16:52:58.471 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 16:52:58.471 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 16:52:58.471 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 16:52:58.472 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 16:52:58.475 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 16:52:58.475 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 16:52:58.530 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 16:52:58.576 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 16:52:59.190 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-08-05 16:52:58.339000
2025-08-05 16:52:59.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 16:52:59.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:52:59.474 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 42975dcc-71d9-11f0-8035-dc4546c07870; requestId: 9b095e8e-cb15-4688-8360-4fc180dd5127_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-08-05 16:52:59.474 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 16:52:59.474 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 16:53:01.347 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 16:53:00.495000
2025-08-05 16:53:01.356 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 16:53:01.356 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 16:53:01.357 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 16:53:02.498 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 16:53:01.621000
2025-08-05 16:53:02.498 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1126
2025-08-05 16:53:02.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:53:02.865 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:53:03.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 16:53:03.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 16:58:22.013 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 16:58:22.013 - chat_with_robot - voice.py - stop - line 436 - INFO - 已停止local_streaming检测器

2025-08-05 17:00:57.309 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-05 17:00:57.309 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 17:00:57.316 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754384457316&accessNonce=6923e95d-f18d-45d2-ae03-d95f7bda3e1c&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=b348681e-71da-11f0-8156-dc4546c07870&requestId=08480b11-b190-4f6c-adff-06ea119350f4_joyinside&accessSign=b5306a5abb6fdc85b286fe8b96409521, request_id: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside
2025-08-05 17:00:57.317 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 17:00:57.317 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 17:00:57.724 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 17:00:58.055 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 17:00:59.667 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 17:00:59.669 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 17:00:59.669 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 17:00:59.669 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 17:00:59.734 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 17:00:59.735 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 17:01:00.735 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:01:00.735 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:01:03.740 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:01:03.740 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 17:01:03.741 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 17:01:03.745 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 17:01:03.745 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-05 17:01:03.745 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 17:01:04.734 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:01:04.734 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:01:04.797 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:01:04.797 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:01:04.797 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:01:04.797 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:01:04.799 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:01:04.799 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:01:04.799 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:01:04.802 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:01:04.802 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:01:04.853 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:01:04.903 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:01:07.122 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:01:07.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:01:08.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:01:08.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:01:10.980 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆语音, 时间戳: 2025-08-05 17:01:10.125000
2025-08-05 17:01:12.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:01:11.284000
2025-08-05 17:01:12.163 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1159
2025-08-05 17:01:12.175 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:12.178 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:12.178 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:12.178 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:12.178 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:12.178 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:01:12.178 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:12.503 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:12.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:12.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:12.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:13.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:13.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:13.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:13.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:13.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:13.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:01:13.478 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:13.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: b348681e-71da-11f0-8156-dc4546c07870; requestId: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside; asr: 我想克隆语音; 响应时间: 0; JD机器人回复: 好呀！我们可以一起玩声音魔法哦。首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦！请跟我读：“今天天气真好呀，我们一起去草地上打滚吧！”
2025-08-05 17:01:13.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:01:14.803 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:14.803 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:14.803 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:14.804 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:14.804 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:14.804 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 17:01:14.804 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 17:01:17.430 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:19.844 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13176 bytes, 持续时间: 2.0
2025-08-05 17:01:19.845 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:22.266 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:22.266 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:22.266 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:22.267 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:22.267 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:22.267 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 17:01:22.267 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:24.686 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:24.686 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:24.686 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:01:24.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:01:36.308 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是不一样的，我没看, 时间戳: 2025-08-05 17:01:35.453000
2025-08-05 17:01:37.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:01:36.556000
2025-08-05 17:01:37.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1103
2025-08-05 17:01:37.455 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:37.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:37.459 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:37.459 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:37.459 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:37.459 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 17:01:37.459 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:37.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:01:37.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:01:37.502 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:01:37.502 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:01:37.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: b348681e-71da-11f0-8156-dc4546c07870; requestId: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside; asr: 是不一样的，我没看; 响应时间: 0; JD机器人回复: 哎呀，看来我可能没说明白呢。
2025-08-05 17:01:37.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:01:37.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:01:37.603 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:01:39.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你一定要一样，它这个一样, 时间戳: 2025-08-05 17:01:38.226000
2025-08-05 17:01:39.882 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:39.882 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:39.882 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:01:40.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:01:39.259000
2025-08-05 17:01:40.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1033
2025-08-05 17:01:40.154 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:40.156 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:40.156 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:40.156 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:40.157 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:40.158 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14688 bytes, 持续时间: 2.0
2025-08-05 17:01:40.158 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:40.432 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:40.436 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:40.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:40.824 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:41.115 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:41.117 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:41.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:41.384 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:41.571 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:01:41.572 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:01:41.580 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:01:41.580 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:01:41.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: b348681e-71da-11f0-8156-dc4546c07870; requestId: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside; asr: 你一定要一样，它这个一样; 响应时间: 0; JD机器人回复: 哎呀，是不是觉得有点乱乱的呀？我完全理解呢！声音魔法需要特别小心，因为每个人的声音都是独一无二的，就像我们的指纹一样。为了保护你的声音安全，
2025-08-05 17:01:41.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:01:41.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:01:41.681 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:01:42.689 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:42.689 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:42.689 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:01:43.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你再去合成一个呗, 时间戳: 2025-08-05 17:01:42.296000
2025-08-05 17:01:44.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:01:43.481000
2025-08-05 17:01:44.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1185
2025-08-05 17:01:44.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:44.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:44.382 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:44.382 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:44.382 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:44.382 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11880 bytes, 持续时间: 2.0
2025-08-05 17:01:44.382 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:44.695 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:44.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:01:44.706 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:01:44.706 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:44.706 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:01:44.716 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: b348681e-71da-11f0-8156-dc4546c07870; requestId: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside; asr: ，你再去合成一个呗; 响应时间: 0; JD机器人回复: 好哦！那我换一个简单的，请跟我读：“今天天气真好呀，
2025-08-05 17:01:44.717 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:01:44.807 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:01:44.908 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:01:46.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你要做和他一模一样才行, 时间戳: 2025-08-05 17:01:45.924000
2025-08-05 17:01:46.800 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:46.800 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:46.800 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:01:46.800 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:01:47.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:01:46.928000
2025-08-05 17:01:47.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1004
2025-08-05 17:01:47.819 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:47.824 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:47.824 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:47.824 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:47.824 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:47.824 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11016 bytes, 持续时间: 2.0
2025-08-05 17:01:47.824 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:48.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:48.084 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:48.415 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:48.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:48.733 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:48.744 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:48.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:01:48.776 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:01:48.786 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:01:48.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: b348681e-71da-11f0-8156-dc4546c07870; requestId: 08480b11-b190-4f6c-adff-06ea119350f4_joyinside; asr: ，你要做和他一模一样才行; 响应时间: 0; JD机器人回复: 好哦！那我们再试一次，这次会更简单有趣呢。请跟我读：“小猫咪跳上窗台，阳光暖暖的，真舒服呀！”
2025-08-05 17:01:48.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:01:49.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:01:50.245 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:50.245 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:50.246 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:50.246 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:50.246 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:50.246 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10368 bytes, 持续时间: 2.0
2025-08-05 17:01:50.246 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:52.665 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13176 bytes, 持续时间: 2.0
2025-08-05 17:01:52.666 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11664 bytes, 持续时间: 2.0
2025-08-05 17:01:55.085 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:01:56.841 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 17:01:56.842 - chat_with_robot - voice.py - stop - line 436 - INFO - 已停止local_streaming检测器
2025-08-05 17:01:57.504 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:01:57.505 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:01:57.505 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:01:57.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续

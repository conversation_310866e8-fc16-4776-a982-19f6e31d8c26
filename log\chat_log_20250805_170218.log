2025-08-05 17:02:19.881 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-05 17:02:19.882 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 17:02:19.888 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754384539887&accessNonce=c813908e-6159-43ec-845d-b496d9ea2d1a&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=e47fc34a-71da-11f0-a2e8-dc4546c07870&requestId=22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside&accessSign=16687f7fa8fc12d540e60134c94ea901, request_id: 22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside
2025-08-05 17:02:19.889 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 17:02:19.889 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 17:02:20.255 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 17:02:20.500 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 17:02:22.102 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 17:02:22.103 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 17:02:22.103 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 17:02:22.103 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 17:02:22.167 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 17:02:22.167 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 17:02:23.167 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:02:23.168 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:02:26.170 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:02:26.172 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 17:02:26.172 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 17:02:26.177 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 17:02:26.177 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-05 17:02:26.177 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 17:02:37.428 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:02:37.428 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:02:37.491 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:02:37.491 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:02:37.491 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:02:37.492 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:02:37.492 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:02:37.492 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 17:02:37.493 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:02:37.497 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:02:37.497 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:02:37.544 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:02:37.598 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:02:38.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:02:38.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:02:40.671 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆语音, 时间戳: 2025-08-05 17:02:39.814000
2025-08-05 17:02:41.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:02:40.948000
2025-08-05 17:02:41.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1134
2025-08-05 17:02:41.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:41.844 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:41.844 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:41.844 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:41.844 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:41.844 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:02:41.845 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:42.158 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:42.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:42.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:42.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:42.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:42.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:43.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:43.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:43.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:43.109 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:02:43.118 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:43.129 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: e47fc34a-71da-11f0-a2e8-dc4546c07870; requestId: 22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside; asr: 我想克隆语音; 响应时间: 0; JD机器人回复: 好呀，我们可以一起玩声音魔法哦！首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦。请跟我读：“今天天气真好呀，我们一起出去玩滑板车吧！”
2025-08-05 17:02:43.129 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15768 bytes, 持续时间: 2.0
2025-08-05 17:02:44.474 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12096 bytes, 持续时间: 2.0
2025-08-05 17:02:47.204 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:49.620 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 17:02:49.621 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:52.038 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 17:02:52.039 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:54.459 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:02:54.460 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:02:54.460 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:02:54.460 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:02:56.146 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这个你有吗？, 时间戳: 2025-08-05 17:02:55.290000
2025-08-05 17:02:57.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:02:56.526000
2025-08-05 17:02:57.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1236
2025-08-05 17:02:57.427 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:57.436 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:57.436 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:02:57.436 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:02:57.436 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:02:57.436 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 17:02:57.436 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:02:57.759 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:57.770 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:58.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:58.068 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:58.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:58.404 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:58.674 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:58.683 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:58.965 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:58.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:59.247 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:02:59.248 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:02:59.252 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:02:59.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: e47fc34a-71da-11f0-a2e8-dc4546c07870; requestId: 22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside; asr: 这个你有吗？; 响应时间: 0; JD机器人回复: 哎呀，你是不是问我要什么东西呀？声音魔法需要你亲自念一段话才能生效哦，因为每个人的声音都是独一无二的，就像指纹一样，要保护好才行呢！请像施展魔法一样读这句话：“今天天气真好呀，我们一起出去玩滑板车吧！”这样我就能学会你的声音啦！
2025-08-05 17:02:59.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:03:00.068 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:00.068 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:00.068 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:00.068 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:00.069 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:00.069 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17280 bytes, 持续时间: 2.0
2025-08-05 17:03:00.069 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:03:02.998 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:05.625 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:05.625 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:05.625 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:05.626 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:05.626 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:05.626 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14688 bytes, 持续时间: 2.0
2025-08-05 17:03:05.626 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:08.152 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:08.153 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:08.153 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:08.153 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:08.154 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:08.154 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12528 bytes, 持续时间: 2.0
2025-08-05 17:03:08.154 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:10.575 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:10.575 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:10.575 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:10.575 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:10.576 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:10.576 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 19872 bytes, 持续时间: 2.0
2025-08-05 17:03:10.576 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13176 bytes, 持续时间: 2.0
2025-08-05 17:03:13.914 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:03:16.331 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:03:16.331 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:03:16.333 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:03:16.333 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:08:41.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:10:30.334 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:10:30.335 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:11:19.078 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:11:19.079 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:11:19.142 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:11:19.142 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:11:19.142 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:11:19.143 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:11:19.143 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:11:19.143 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:11:19.143 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:11:19.144 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:11:19.144 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:11:19.202 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:11:19.244 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:11:22.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下, 时间戳: 2025-08-05 17:11:21.750000
2025-08-05 17:11:22.611 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:11:22.611 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:11:22.612 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:11:23.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:11:23.023000
2025-08-05 17:11:23.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1273
2025-08-05 17:11:23.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:11:24.229 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:11:24.549 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:11:24.550 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:11:57.548 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:11:57.548 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:11:57.612 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:11:57.613 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:11:57.613 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:11:57.613 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:11:57.613 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:11:57.613 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 17:11:57.614 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:11:57.623 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:11:57.623 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:11:57.702 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:11:57.724 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:11:58.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:12:00.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下, 时间戳: 2025-08-05 17:12:00.107000
2025-08-05 17:12:00.973 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:12:00.975 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:12:00.975 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:12:02.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:12:01.163000
2025-08-05 17:12:02.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1056
2025-08-05 17:12:02.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:12:02.349 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:12:02.684 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:12:02.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:13:16.337 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:13:16.338 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:13:16.401 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:13:16.401 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:13:16.401 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:13:16.402 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:13:16.402 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:13:16.402 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:13:16.402 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:13:16.407 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:13:16.407 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:13:16.496 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:13:16.508 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:13:19.934 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 17:13:19.074000
2025-08-05 17:13:19.943 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:13:19.943 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:13:19.943 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:13:20.843 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:13:19.969000
2025-08-05 17:13:20.843 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 895
2025-08-05 17:13:20.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:13:21.147 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:13:21.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:13:21.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:13:21.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:18:16.888 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:18:16.888 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:18:16.957 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:18:16.957 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:18:16.957 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:18:16.957 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:18:16.958 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:18:16.958 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:18:16.958 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:18:16.961 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:18:16.961 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:18:17.039 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:18:17.062 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:18:18.459 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 什么事？, 时间戳: 2025-08-05 17:18:17.597000
2025-08-05 17:18:19.285 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:18:19.286 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:18:19.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: e47fc34a-71da-11f0-a2e8-dc4546c07870; requestId: 22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside; asr: 什么事？; 响应时间: 0; JD机器人回复: 
2025-08-05 17:18:19.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:18:19.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:18:19.294 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:18:19.294 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:18:19.395 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:18:20.493 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要克隆语音, 时间戳: 2025-08-05 17:18:19.630000
2025-08-05 17:18:22.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:18:21.151000
2025-08-05 17:18:22.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1521
2025-08-05 17:18:22.045 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:22.051 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:22.051 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:18:22.051 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:18:22.052 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:18:22.052 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 17:18:22.052 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:18:22.390 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:22.394 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:22.691 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:22.699 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:22.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:23.004 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:23.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:23.276 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:23.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:18:23.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:18:23.319 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:18:23.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: e47fc34a-71da-11f0-a2e8-dc4546c07870; requestId: 22770f3a-e4fe-4dc1-91bb-f2414332254f_joyinside; asr: 我要克隆语音; 响应时间: 0; JD机器人回复: 好呀，克隆你的声音需要你同意哦，请来读一段小小的魔法咒语，我就能学会你的声音啦。请跟我读：“今天天气真好呀，我们一起去草地上踢足球吧！”
2025-08-05 17:18:23.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:18:24.682 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13176 bytes, 持续时间: 2.0
2025-08-05 17:18:24.683 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:18:27.101 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:18:27.101 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:18:27.101 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:18:27.102 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:18:27.102 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:18:27.102 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10800 bytes, 持续时间: 2.0
2025-08-05 17:18:27.102 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:18:29.519 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:18:29.519 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:18:29.519 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:18:29.519 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:18:29.520 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:18:29.520 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13392 bytes, 持续时间: 2.0
2025-08-05 17:18:29.520 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 17:18:31.943 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:18:34.360 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:18:34.360 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:18:34.360 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:18:34.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:18:37.635 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:18:37.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:18:38.338 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 17:18:38.338 - chat_with_robot - voice.py - stop - line 436 - INFO - 已停止local_streaming检测器

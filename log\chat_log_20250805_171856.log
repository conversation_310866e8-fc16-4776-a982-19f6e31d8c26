2025-08-05 17:18:57.663 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-05 17:18:57.663 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 17:18:57.668 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754385537669&accessNonce=0171833a-ff9c-4204-a7e2-d7019ed84c22&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=37391b5f-71dd-11f0-8166-dc4546c07870&requestId=c35fee12-6375-4fef-ad64-8d836d67b7f1_joyinside&accessSign=e64bf2a3c5ef83df298520df1df77f1c, request_id: c35fee12-6375-4fef-ad64-8d836d67b7f1_joyinside
2025-08-05 17:18:57.670 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 17:18:57.670 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 17:18:58.205 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 17:18:58.423 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 17:19:00.052 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 17:19:00.053 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 17:19:00.053 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 17:19:00.053 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 17:19:00.111 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 17:19:00.112 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 17:19:01.113 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:19:01.113 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:19:04.117 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:19:04.117 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 17:19:04.118 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 17:19:04.123 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 17:19:04.123 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-05 17:19:04.123 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 17:19:10.572 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:19:10.573 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:19:10.637 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:19:10.637 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:19:10.637 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:19:10.638 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:19:10.638 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:19:10.638 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:19:10.638 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:19:10.645 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:19:10.646 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:19:10.653 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:19:10.747 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:19:15.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要克隆语音, 时间戳: 2025-08-05 17:19:14.754000
2025-08-05 17:19:16.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:19:15.986000
2025-08-05 17:19:16.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1232
2025-08-05 17:19:16.890 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:16.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:16.892 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:16.892 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:16.892 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:16.892 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:19:16.892 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:17.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:17.173 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:17.450 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:17.453 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:17.769 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:17.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:18.056 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:18.058 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:18.110 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:18.112 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:19:18.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:18.132 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 37391b5f-71dd-11f0-8166-dc4546c07870; requestId: c35fee12-6375-4fef-ad64-8d836d67b7f1_joyinside; asr: 我要克隆语音; 响应时间: 0; JD机器人回复: 好呀！我们可以一起玩声音魔法哦。首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦！请跟我读：“今天天气真好呀，我们一起去草地上踢足球吧！”
2025-08-05 17:19:18.132 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:19:19.521 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:19:19.521 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:19:19.521 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:19.521 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:19.522 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:19.522 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15984 bytes, 持续时间: 2.0
2025-08-05 17:19:19.522 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 17:19:22.247 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:24.667 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:19:24.667 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:19:24.667 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:24.667 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:24.667 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:24.668 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 17:19:24.669 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:27.094 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:19:27.094 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:19:27.094 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:27.094 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:27.094 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:27.095 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12744 bytes, 持续时间: 2.0
2025-08-05 17:19:27.095 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:29.513 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:19:29.513 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:19:29.515 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:19:29.515 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:19:39.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯，或者你把那上面, 时间戳: 2025-08-05 17:19:38.176000
2025-08-05 17:19:40.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:19:39.294000
2025-08-05 17:19:40.175 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1118
2025-08-05 17:19:40.199 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:40.205 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:40.205 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:19:40.206 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:19:40.206 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:19:40.206 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14256 bytes, 持续时间: 2.0
2025-08-05 17:19:40.206 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:19:40.583 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:40.590 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:40.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:40.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:41.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:41.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:19:41.486 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:19:41.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频

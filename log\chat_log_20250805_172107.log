2025-08-05 17:21:09.573 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-05 17:21:09.574 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 17:21:09.580 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754385669580&accessNonce=3786954a-36e2-4701-8588-0dc5992b3491&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=85d93e42-71dd-11f0-8b61-dc4546c07870&requestId=54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside&accessSign=558013daa20f873200c2107d7de73d66, request_id: 54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside
2025-08-05 17:21:09.580 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 17:21:09.580 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 17:21:09.977 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 17:21:10.141 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 17:21:12.300 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 17:21:12.300 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 17:21:12.301 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 17:21:12.301 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 17:21:12.376 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 17:21:12.376 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 17:21:13.377 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:21:13.377 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:21:16.382 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:21:16.382 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 17:21:16.383 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 17:21:16.387 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 17:21:16.387 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-05 17:21:16.387 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 17:21:26.976 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:21:26.976 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:21:27.039 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:21:27.041 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:21:27.041 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:21:27.042 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:21:27.042 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:21:27.042 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:21:27.042 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:21:27.050 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:21:27.051 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:21:27.143 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:21:27.152 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:21:30.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆语音, 时间戳: 2025-08-05 17:21:29.952000
2025-08-05 17:21:31.966 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:21:31.077000
2025-08-05 17:21:31.966 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1125
2025-08-05 17:21:31.987 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:31.989 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:31.989 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:21:31.989 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:21:31.989 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:21:31.989 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 17:21:31.989 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:21:32.312 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:32.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:32.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:32.627 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:32.925 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:32.932 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:33.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:33.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:33.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:21:33.266 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:21:33.268 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:21:33.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 85d93e42-71dd-11f0-8b61-dc4546c07870; requestId: 54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside; asr: 我想克隆语音; 响应时间: 0; JD机器人回复: 好呀，我们可以一起玩声音魔法哦！首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦。请跟我读：“今天天气真好呀，我们一起出去玩滑板车吧！”
2025-08-05 17:21:33.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 17:21:34.624 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 17:21:37.253 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 17:21:39.676 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:21:40.760 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 17:21:42.095 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:21:44.518 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:21:44.518 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:21:44.518 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:21:44.518 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:21:48.677 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:21:48.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:22:04.189 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:22:04.190 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:22:04.251 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:22:04.252 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:22:04.252 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:22:04.252 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:22:04.252 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:22:04.252 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 17:22:04.253 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:22:04.255 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:22:04.255 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:22:04.311 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:22:04.356 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:22:04.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-08-05 17:22:03.869000
2025-08-05 17:22:05.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:22:05.005000
2025-08-05 17:22:05.887 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1136
2025-08-05 17:22:05.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:05.912 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:05.912 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:05.912 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:05.912 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:05.912 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:22:05.912 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:06.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:22:06.158 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:22:06.164 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:22:06.164 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:22:06.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 85d93e42-71dd-11f0-8b61-dc4546c07870; requestId: 54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 好呀！我们可以一起玩声音魔法哦！
2025-08-05 17:22:06.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:22:06.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:22:06.266 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:22:07.617 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想克隆语音, 时间戳: 2025-08-05 17:22:06.753000
2025-08-05 17:22:08.538 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:08.538 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:08.538 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:22:08.916 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:22:08.032000
2025-08-05 17:22:08.916 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1279
2025-08-05 17:22:08.931 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:08.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:08.942 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:08.942 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:08.942 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:08.942 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15768 bytes, 持续时间: 2.0
2025-08-05 17:22:08.942 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:09.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:09.214 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:09.516 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:09.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:09.808 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:09.815 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:10.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:10.075 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:10.113 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:22:10.114 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:22:10.118 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:22:10.128 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 85d93e42-71dd-11f0-8b61-dc4546c07870; requestId: 54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside; asr: ，我想克隆语音; 响应时间: 0; JD机器人回复: 好呀！我们可以一起玩声音魔法哦。首先需要你来读一段小小的魔法咒语，这样我就能学会你的声音啦！请跟我读：“今天天气真好呀，我们一起出去玩滑板车吧！”
2025-08-05 17:22:10.128 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15768 bytes, 持续时间: 2.0
2025-08-05 17:22:11.671 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 17:22:14.401 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:16.822 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 17:22:16.823 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:19.245 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 17:22:19.246 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:22:21.665 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:22:21.665 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:22:21.665 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:22:21.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:22:24.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:22:58.977 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:22:58.978 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:22:59.041 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:22:59.041 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:22:59.041 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:22:59.041 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:22:59.042 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:22:59.042 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 17:22:59.042 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:22:59.044 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:22:59.044 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:22:59.101 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:22:59.146 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:22:59.528 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好东东, 时间戳: 2025-08-05 17:22:58.663000
2025-08-05 17:23:00.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:22:59.780000
2025-08-05 17:23:00.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1117
2025-08-05 17:23:00.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:00.689 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-05 17:23:00.689 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 17:23:00.690 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 17:23:00.690 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 17:23:00.690 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-08-05 17:23:00.690 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 17:23:00.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 17:23:00.848 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:23:00.851 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:23:00.851 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:23:00.854 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 85d93e42-71dd-11f0-8b61-dc4546c07870; requestId: 54426ad3-6058-4b09-ab05-fca83a222e5d_joyinside; asr: 你好东东; 响应时间: 0; JD机器人回复: 好呀，我们可以一起玩声音魔法哦！
2025-08-05 17:23:00.854 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-05 17:23:00.854 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-05 17:23:00.952 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:23:01.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 17:23:00.574000
2025-08-05 17:23:01.445 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:23:01.445 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:23:01.446 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:23:02.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:23:02.007000
2025-08-05 17:23:02.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1433
2025-08-05 17:23:02.904 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:03.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:03.551 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:03.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:04.115 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:23:04.120 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 17:23:04.146 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:23:04.148 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:23:04.210 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:23:04.210 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:23:04.210 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:23:04.211 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:23:04.211 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 17:23:04.211 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 17:23:04.211 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 17:23:04.212 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 17:23:04.212 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 17:23:04.212 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:23:04.214 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:23:04.221 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:23:04.221 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:23:04.322 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:23:06.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:25:28.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:25:28.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:25:59.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:25:59.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:27.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:27.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:28.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:28.865 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:30.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:30.304 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:31.744 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:31.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:33.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:33.172 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:34.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:34.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:36.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:36.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:37.620 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:37.620 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:39.050 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:39.050 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:40.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:40.379 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:41.817 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:41.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:27:42.654 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:27:42.655 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:28:06.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:28:06.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:29:47.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:29:47.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:29:48.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:29:48.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:30:11.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:30:11.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:30:12.776 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:30:12.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:30:21.016 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:30:21.016 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:30:21.081 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:30:21.081 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:30:21.081 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:30:21.082 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:30:21.082 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:30:21.082 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:30:21.082 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:30:21.087 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:30:21.087 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:30:21.141 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:30:21.188 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:30:24.628 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 17:30:24.628 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 17:30:24.691 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 17:30:24.691 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:30:24.691 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:30:24.692 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 17:30:24.692 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 17:30:24.692 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 17:30:24.692 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 17:30:24.700 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 17:30:24.700 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 17:30:24.751 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 17:30:24.802 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 17:30:27.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 17:30:27.196 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 17:30:31.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-08-05 17:30:30.886000
2025-08-05 17:30:31.765 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 17:30:31.765 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 17:30:31.766 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 17:30:32.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 17:30:31.824000
2025-08-05 17:30:32.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 938
2025-08-05 17:30:32.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:30:33.005 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:30:33.252 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 17:30:33.252 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE

2025-08-11 09:27:18.359 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: ./asserts/tts/network_tts.mp3
2025-08-11 09:27:18.652 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: ./asserts/tts/network_tts.mp3
2025-08-11 09:27:18.653 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-11 09:27:18.653 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-11 09:27:18.664 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754875638664&accessNonce=22d215ad-c37f-46ed-bdcc-d236628213bb&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=522e2029-7652-11f0-85d6-dc4546c07870&requestId=ae3df795-6f14-463a-b2eb-88a6d140c5de_joyinside&accessSign=3e97770da639082b3900ac178f99588d, request_id: ae3df795-6f14-463a-b2eb-88a6d140c5de_joyinside
2025-08-11 09:27:18.664 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-11 09:27:18.664 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-11 09:27:18.667 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: [Errno 11001] getaddrinfo failed
2025-08-11 09:27:18.667 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-08-11 09:27:18.667 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-11 09:27:18.667 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-08-11 09:27:23.690 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-11 09:27:25.172 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-11 09:27:25.175 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-11 09:27:25.175 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-11 09:27:25.175 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-11 09:27:25.259 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-11 09:27:25.259 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-11 09:27:26.261 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-11 09:27:26.261 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-11 09:27:26.262 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-11 09:27:26.262 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-11 09:27:26.263 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-11 09:27:26.271 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-11 09:27:26.271 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-11 09:27:26.271 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-11 09:27:39.401 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-11 09:27:39.402 - chat_with_robot - voice.py - stop - line 436 - INFO - 已停止local_streaming检测器

2025-08-12 09:22:02.209 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: ./asserts/tts/network_tts.mp3
2025-08-12 09:22:02.583 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: ./asserts/tts/network_tts.mp3
2025-08-12 09:22:02.585 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-12 09:22:02.585 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-12 09:22:02.592 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754961722591&accessNonce=6c359dd8-dde8-4531-b73e-d90d92925e66&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=c032f221-771a-11f0-8441-dc4546c07870&requestId=366d5175-a849-478e-b7cc-d291c92be08a_joyinside&accessSign=bf4fa2bba4fbc9b0c709469e3ddd7c63, request_id: 366d5175-a849-478e-b7cc-d291c92be08a_joyinside
2025-08-12 09:22:02.592 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-12 09:22:02.592 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-12 09:22:02.596 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: [Errno 11001] getaddrinfo failed
2025-08-12 09:22:02.596 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-08-12 09:22:02.596 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-12 09:22:02.596 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-08-12 09:22:07.621 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动

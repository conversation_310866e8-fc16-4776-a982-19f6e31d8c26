2025-08-12 09:24:31.610 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - use_action: dont
2025-08-12 09:24:31.610 - chat_with_robot - chat_with_robot.py - <module> - line 923 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-12 09:24:31.617 - chat_with_robot - chat_with_robot.py - init_websocket - line 534 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754961871618&accessNonce=1143d31e-6aac-4685-b688-de5632ddfdd1&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=19069027-771b-11f0-9596-dc4546c07870&requestId=e9df3e65-25f7-4074-911f-e5401eb84017_joyinside&accessSign=f1392cd6f372a8f87bf8859bee0bed38, request_id: e9df3e65-25f7-4074-911f-e5401eb84017_joyinside
2025-08-12 09:24:31.617 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-12 09:24:31.617 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-12 09:24:32.041 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-12 09:24:32.366 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-12 09:24:33.750 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-12 09:24:33.752 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-12 09:24:33.752 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-12 09:24:33.752 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-12 09:24:33.819 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-12 09:24:33.819 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-12 09:24:34.819 - chat_with_robot - chat_with_robot.py - play_audio - line 797 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-12 09:24:34.819 - chat_with_robot - chat_with_robot.py - play_audio - line 805 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-12 09:24:37.825 - chat_with_robot - chat_with_robot.py - play_audio - line 807 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-12 09:24:37.829 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-12 09:24:37.829 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - API地址: http://localhost:8080/tts
2025-08-12 09:24:37.829 - chat_with_robot - chat_with_robot.py - start_http_server - line 491 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-12 09:24:45.539 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-12 09:24:45.539 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-12 09:24:45.608 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-12 09:24:45.609 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-12 09:24:45.609 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-12 09:24:45.609 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-12 09:24:45.609 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-12 09:24:45.611 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-12 09:24:45.611 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-12 09:24:45.611 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-12 09:24:45.611 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-12 09:24:45.670 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-12 09:24:45.712 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 605 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-12 09:24:47.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-12 09:24:47.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-12 09:24:48.765 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气怎么样？, 时间戳: 2025-08-12 09:24:48.045000
2025-08-12 09:24:50.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-12 09:24:49.614000
2025-08-12 09:24:50.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1569
2025-08-12 09:24:50.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:50.373 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:50.373 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:24:50.373 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:24:50.373 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:24:50.373 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15768 bytes, 持续时间: 2.0
2025-08-12 09:24:50.373 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:24:50.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:50.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:51.229 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:51.233 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:51.493 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:51.497 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:51.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:51.911 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:52.053 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:24:52.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-12 09:24:52.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:24:52.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 19069027-771b-11f0-9596-dc4546c07870; requestId: e9df3e65-25f7-4074-911f-e5401eb84017_joyinside; asr: 今天天气怎么样？; 响应时间: 0; JD机器人回复: 今天北京有中雨，夜晚转为雷阵雨，温度在23°到30°之间，体感温度28°，北风2级，相对湿度77%，空气质量不错。出门记得带伞，小心路滑，注意安全！
2025-08-12 09:24:52.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-12 09:24:53.008 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:24:53.008 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:24:53.008 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:24:53.009 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:24:53.009 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:24:53.009 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-12 09:24:53.009 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-12 09:24:55.429 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:24:57.849 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17064 bytes, 持续时间: 2.0
2025-08-12 09:24:57.850 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12528 bytes, 持续时间: 2.0
2025-08-12 09:25:00.791 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:03.217 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:03.217 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:03.218 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:03.218 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:03.218 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6048 bytes, 持续时间: 0.369140625
2025-08-12 09:25:03.218 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:04.235 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:04.235 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:04.235 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-12 09:25:04.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-12 09:25:08.454 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天成都天气怎么样？, 时间戳: 2025-08-12 09:25:07.734000
2025-08-12 09:25:09.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-12 09:25:09.068000
2025-08-12 09:25:09.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1334
2025-08-12 09:25:09.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:09.824 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:09.824 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:09.824 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:09.824 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:09.824 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 19440 bytes, 持续时间: 2.0
2025-08-12 09:25:09.824 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:10.128 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:10.132 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:10.375 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:10.384 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:10.848 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:10.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:11.008 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:11.010 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:11.380 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:11.390 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:11.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:11.556 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-12 09:25:11.566 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:11.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 19069027-771b-11f0-9596-dc4546c07870; requestId: e9df3e65-25f7-4074-911f-e5401eb84017_joyinside; asr: 今天成都天气怎么样？; 响应时间: 0; JD机器人回复: 今天成都天气晴朗，白天最高气温34度，最低气温22度。现在的体感温度是32度，空气质量不错，相对湿度为76%，风力不大，东南风2级。建议外出时注意防晒，紫外线很强。适合外出活动，但记得带上防晒用品。
2025-08-12 09:25:11.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 20520 bytes, 持续时间: 2.0
2025-08-12 09:25:13.167 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:16.611 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:16.611 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:16.612 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:16.613 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:16.613 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:16.613 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17928 bytes, 持续时间: 2.0
2025-08-12 09:25:16.613 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:19.650 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:19.650 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:19.651 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:19.651 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:19.651 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:19.651 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11664 bytes, 持续时间: 2.0
2025-08-12 09:25:19.651 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 9936 bytes, 持续时间: 2.0
2025-08-12 09:25:22.076 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:24.494 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13392 bytes, 持续时间: 2.0
2025-08-12 09:25:24.495 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:26.915 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:26.915 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:26.915 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:26.916 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:26.916 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:26.916 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10368 bytes, 持续时间: 2.0
2025-08-12 09:25:26.917 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:29.339 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:29.339 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:29.339 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-12 09:25:29.340 - chat_with_robot - chat_with_robot.py - _task_worker - line 663 - INFO - 任务完成，继续
2025-08-12 09:25:32.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 回锅肉怎么做？, 时间戳: 2025-08-12 09:25:31.383000
2025-08-12 09:25:32.997 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-12 09:25:32.254000
2025-08-12 09:25:32.997 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 871
2025-08-12 09:25:32.997 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:32.999 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:32.999 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:32.999 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:32.999 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:32.999 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 18144 bytes, 持续时间: 2.0
2025-08-12 09:25:32.999 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:33.173 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:33.180 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:33.508 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:33.509 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:33.680 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:33.683 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:33.845 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:33.845 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.008 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.179 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.363 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.538 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.723 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.732 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:34.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:34.883 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:35.040 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-12 09:25:35.043 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-12 09:25:35.049 - chat_with_robot - chat_with_robot.py - _task_worker - line 709 - INFO - 存入音频
2025-08-12 09:25:35.060 - chat_with_robot - chat_with_robot.py - _task_worker - line 645 - INFO - session_id: 19069027-771b-11f0-9596-dc4546c07870; requestId: e9df3e65-25f7-4074-911f-e5401eb84017_joyinside; asr: 回锅肉怎么做？; 响应时间: 0; JD机器人回复: 哇～说起回锅肉，我的口水都要流下来啦！虽然我可能不太会做，但是听妈妈说过怎么做哦～  

首先，要选一块带皮的五花肉，先用水煮到七八分熟，然后切片。接着锅里放油，把肉片炒到边缘微微卷起来，盛出来备用。再用剩下的油炒香蒜末和豆瓣酱，把肉片倒回去一起炒，加点酱油、糖调味，最后放青椒或者蒜苗翻炒几下就可以出锅啦！  

听起来是不是超好吃？你想试试做吗？😋
2025-08-12 09:25:35.061 - chat_with_robot - chat_with_robot.py - _task_worker - line 648 - INFO - 等待音频播放完成
2025-08-12 09:25:36.131 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:36.131 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:36.131 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:36.133 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:36.133 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:36.133 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 9936 bytes, 持续时间: 2.0
2025-08-12 09:25:36.133 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12960 bytes, 持续时间: 2.0
2025-08-12 09:25:38.549 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:40.965 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:40.965 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:40.965 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:40.965 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:40.966 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:40.966 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14688 bytes, 持续时间: 2.0
2025-08-12 09:25:40.966 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-12 09:25:43.494 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-12 09:25:43.495 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-12 09:25:43.495 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-12 09:25:43.496 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-12 09:25:43.496 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-12 09:25:43.496 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 9936 bytes, 持续时间: 2.0
2025-08-12 09:25:43.496 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
